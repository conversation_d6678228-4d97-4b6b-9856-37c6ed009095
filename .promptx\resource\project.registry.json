{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-05T07:36:41.159Z", "updatedAt": "2025-08-05T07:36:41.166Z", "resourceCount": 6}, "resources": [{"id": "baota-deploy-guide", "source": "project", "protocol": "role", "name": "Baota Deploy Guide 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/baota-deploy-guide/baota-deploy-guide.role.md", "metadata": {"createdAt": "2025-08-05T07:36:41.161Z", "updatedAt": "2025-08-05T07:36:41.161Z", "scannedAt": "2025-08-05T07:36:41.161Z", "path": "role/baota-deploy-guide/baota-deploy-guide.role.md"}}, {"id": "baota-deployment-workflow", "source": "project", "protocol": "execution", "name": "Baota Deployment Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/baota-deploy-guide/execution/baota-deployment-workflow.execution.md", "metadata": {"createdAt": "2025-08-05T07:36:41.162Z", "updatedAt": "2025-08-05T07:36:41.162Z", "scannedAt": "2025-08-05T07:36:41.162Z", "path": "role/baota-deploy-guide/execution/baota-deployment-workflow.execution.md"}}, {"id": "baota-deployment-thinking", "source": "project", "protocol": "thought", "name": "Baota Deployment Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/baota-deploy-guide/thought/baota-deployment-thinking.thought.md", "metadata": {"createdAt": "2025-08-05T07:36:41.163Z", "updatedAt": "2025-08-05T07:36:41.163Z", "scannedAt": "2025-08-05T07:36:41.163Z", "path": "role/baota-deploy-guide/thought/baota-deployment-thinking.thought.md"}}, {"id": "design-management-workflow", "source": "project", "protocol": "execution", "name": "Design Management Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ui-design-manager/execution/design-management-workflow.execution.md", "metadata": {"createdAt": "2025-08-05T07:36:41.164Z", "updatedAt": "2025-08-05T07:36:41.164Z", "scannedAt": "2025-08-05T07:36:41.164Z", "path": "role/ui-design-manager/execution/design-management-workflow.execution.md"}}, {"id": "design-leadership", "source": "project", "protocol": "thought", "name": "Design Leadership 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-design-manager/thought/design-leadership.thought.md", "metadata": {"createdAt": "2025-08-05T07:36:41.165Z", "updatedAt": "2025-08-05T07:36:41.165Z", "scannedAt": "2025-08-05T07:36:41.165Z", "path": "role/ui-design-manager/thought/design-leadership.thought.md"}}, {"id": "ui-design-manager", "source": "project", "protocol": "role", "name": "Ui Design Manager 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ui-design-manager/ui-design-manager.role.md", "metadata": {"createdAt": "2025-08-05T07:36:41.165Z", "updatedAt": "2025-08-05T07:36:41.165Z", "scannedAt": "2025-08-05T07:36:41.165Z", "path": "role/ui-design-manager/ui-design-manager.role.md"}}], "stats": {"totalResources": 6, "byProtocol": {"role": 2, "execution": 2, "thought": 2}, "bySource": {"project": 6}}}